#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
题目生成器 - 将JSON格式的题目数据转换为HTML答题页面
"""

import json
import html

def load_questions(filename):
    """加载题目数据"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            data = json.loads(content)
        return data
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        print(f"文件内容前100个字符: {content[:100]}")
        raise

def generate_html_page(data):
    """生成HTML答题页面"""
    
    # 获取所有题目类型
    judge_questions = data['data']['table']['judge']  # 判断题
    single_questions = data['data']['table']['single']  # 单选题
    multi_questions = data['data']['table']['multi']  # 多选题
    topics = data['data']['topics']  # 题目详情
    
    # 创建题目ID到题目内容的映射
    topic_dict = {topic['id']: topic for topic in topics}
    
    html_content = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络安全知识答题系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #007bff;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 2.5em;
        }
        .section {
            margin-bottom: 40px;
        }
        .section-title {
            background-color: #007bff;
            color: white;
            padding: 15px;
            margin: 0 0 20px 0;
            border-radius: 5px;
            font-size: 1.3em;
            font-weight: bold;
        }
        .question {
            background-color: #f8f9fa;
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .question-title {
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 1.1em;
            color: #333;
        }
        .options {
            margin-left: 20px;
        }
        .option {
            margin: 8px 0;
            display: flex;
            align-items: center;
        }
        .option input {
            margin-right: 10px;
            transform: scale(1.2);
        }
        .option label {
            cursor: pointer;
            flex: 1;
        }
        .submit-btn {
            background-color: #28a745;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 1.2em;
            cursor: pointer;
            display: block;
            margin: 30px auto;
            transition: background-color 0.3s;
        }
        .submit-btn:hover {
            background-color: #218838;
        }
        .progress {
            background-color: #e9ecef;
            border-radius: 10px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        .progress-bar {
            background-color: #007bff;
            height: 20px;
            width: 0%;
            transition: width 0.3s;
            text-align: center;
            line-height: 20px;
            color: white;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>网络安全知识答题系统</h1>
            <p>请认真阅读每道题目，选择正确答案</p>
        </div>
        
        <div class="progress">
            <div class="progress-bar" id="progressBar">0%</div>
        </div>
        
        <form id="quizForm">
"""
    
    # 生成判断题部分
    if judge_questions:
        html_content += """
            <div class="section">
                <h2 class="section-title">一、判断题</h2>
"""
        for i, q in enumerate(judge_questions[:35], 1):  # 限制显示前35题
            if q['id'] in topic_dict:
                topic = topic_dict[q['id']]
                question_text = html.escape(topic['name'])
                html_content += f"""
                <div class="question">
                    <div class="question-title">{i}. {question_text}</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_{q['id']}_true" name="judge_{q['id']}" value="true">
                            <label for="judge_{q['id']}_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_{q['id']}_false" name="judge_{q['id']}" value="false">
                            <label for="judge_{q['id']}_false">错误</label>
                        </div>
                    </div>
                </div>
"""
        html_content += "            </div>\n"
    
    # 生成单选题部分
    if single_questions:
        html_content += """
            <div class="section">
                <h2 class="section-title">二、单选题</h2>
"""
        for i, q in enumerate(single_questions[:50], 1):  # 限制显示前50题
            if q['id'] in topic_dict:
                topic = topic_dict[q['id']]
                question_text = html.escape(topic['name'])
                html_content += f"""
                <div class="question">
                    <div class="question-title">{i}. {question_text}</div>
                    <div class="options">
"""
                # 添加选项
                options = ['a', 'b', 'c', 'd', 'e', 'f']
                for opt in options:
                    option_text = topic.get(f'option_{opt}', '').strip()
                    if option_text:
                        option_text = html.escape(option_text)
                        html_content += f"""
                        <div class="option">
                            <input type="radio" id="single_{q['id']}_{opt}" name="single_{q['id']}" value="{opt}">
                            <label for="single_{q['id']}_{opt}">{opt.upper()}. {option_text}</label>
                        </div>
"""
                html_content += """
                    </div>
                </div>
"""
        html_content += "            </div>\n"
    
    # 生成多选题部分
    if multi_questions:
        html_content += """
            <div class="section">
                <h2 class="section-title">三、多选题</h2>
"""
        for i, q in enumerate(multi_questions[:30], 1):  # 限制显示前30题
            if q['id'] in topic_dict:
                topic = topic_dict[q['id']]
                question_text = html.escape(topic['name'])
                html_content += f"""
                <div class="question">
                    <div class="question-title">{i}. {question_text}</div>
                    <div class="options">
"""
                # 添加选项
                options = ['a', 'b', 'c', 'd', 'e', 'f']
                for opt in options:
                    option_text = topic.get(f'option_{opt}', '').strip()
                    if option_text:
                        option_text = html.escape(option_text)
                        html_content += f"""
                        <div class="option">
                            <input type="checkbox" id="multi_{q['id']}_{opt}" name="multi_{q['id']}" value="{opt}">
                            <label for="multi_{q['id']}_{opt}">{opt.upper()}. {option_text}</label>
                        </div>
"""
                html_content += """
                    </div>
                </div>
"""
        html_content += "            </div>\n"
    
    # 添加提交按钮和JavaScript
    html_content += """
            <button type="submit" class="submit-btn">提交答案</button>
        </form>
    </div>

    <script>
        // 进度条更新
        function updateProgress() {
            const form = document.getElementById('quizForm');
            const inputs = form.querySelectorAll('input[type="radio"], input[type="checkbox"]');
            const questions = new Set();
            
            inputs.forEach(input => {
                const name = input.name;
                questions.add(name);
            });
            
            let answered = 0;
            questions.forEach(questionName => {
                const questionInputs = form.querySelectorAll(`input[name="${questionName}"]`);
                const isAnswered = Array.from(questionInputs).some(input => input.checked);
                if (isAnswered) answered++;
            });
            
            const progress = (answered / questions.size) * 100;
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = progress + '%';
            progressBar.textContent = Math.round(progress) + '%';
        }
        
        // 监听所有输入变化
        document.addEventListener('change', updateProgress);
        
        // 表单提交处理
        document.getElementById('quizForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const answers = {};
            
            for (let [key, value] of formData.entries()) {
                if (answers[key]) {
                    if (Array.isArray(answers[key])) {
                        answers[key].push(value);
                    } else {
                        answers[key] = [answers[key], value];
                    }
                } else {
                    answers[key] = value;
                }
            }
            
            alert('答案已提交！\\n\\n答题结果：\\n' + JSON.stringify(answers, null, 2));
        });
        
        // 初始化进度条
        updateProgress();
    </script>
</body>
</html>
"""
    
    return html_content

def main():
    """主函数"""
    try:
        # 加载题目数据
        print("正在读取题目数据...")
        data = load_questions('aswer.txt')
        
        # 生成HTML页面
        print("正在生成HTML页面...")
        html_content = generate_html_page(data)
        
        # 保存HTML文件
        output_file = 'quiz.html'
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"HTML答题页面已生成：{output_file}")
        print(f"题目统计：")
        print(f"- 判断题：{len(data['data']['table']['judge'])} 道")
        print(f"- 单选题：{len(data['data']['table']['single'])} 道") 
        print(f"- 多选题：{len(data['data']['table']['multi'])} 道")
        print(f"- 总计：{len(data['data']['topics'])} 道题目")
        
    except FileNotFoundError:
        print("错误：找不到 aswer.txt 文件")
    except json.JSONDecodeError:
        print("错误：JSON文件格式不正确")
    except Exception as e:
        print(f"错误：{str(e)}")

if __name__ == "__main__":
    main()
