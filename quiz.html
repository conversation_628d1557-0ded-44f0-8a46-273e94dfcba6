<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络安全知识答题系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #007bff;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 2.5em;
        }
        .section {
            margin-bottom: 40px;
        }
        .section-title {
            background-color: #007bff;
            color: white;
            padding: 15px;
            margin: 0 0 20px 0;
            border-radius: 5px;
            font-size: 1.3em;
            font-weight: bold;
        }
        .question {
            background-color: #f8f9fa;
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .question-title {
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 1.1em;
            color: #333;
        }
        .options {
            margin-left: 20px;
        }
        .option {
            margin: 8px 0;
            display: flex;
            align-items: center;
        }
        .option input {
            margin-right: 10px;
            transform: scale(1.2);
        }
        .option label {
            cursor: pointer;
            flex: 1;
        }
        .submit-btn {
            background-color: #28a745;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 1.2em;
            cursor: pointer;
            display: block;
            margin: 30px auto;
            transition: background-color 0.3s;
        }
        .submit-btn:hover {
            background-color: #218838;
        }
        .progress {
            background-color: #e9ecef;
            border-radius: 10px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        .progress-bar {
            background-color: #007bff;
            height: 20px;
            width: 0%;
            transition: width 0.3s;
            text-align: center;
            line-height: 20px;
            color: white;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>网络安全知识答题系统</h1>
            <p>请认真阅读每道题目，选择正确答案</p>
        </div>
        
        <div class="progress">
            <div class="progress-bar" id="progressBar">0%</div>
        </div>
        
        <form id="quizForm">

            <div class="section">
                <h2 class="section-title">一、判断题</h2>

                <div class="question">
                    <div class="question-title">1. 要求用户提供真实身份信息是网络运营者的一项法定义务</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21117_true" name="judge_21117" value="true">
                            <label for="judge_21117_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21117_false" name="judge_21117" value="false">
                            <label for="judge_21117_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">2. 配置核查就是对 IT 范围内，漏洞扫描工具不能有效发现的方面（网络设备的安全策略弱点和部分主机的安全配置错误等） 进行安全辅助的一种有效评估手段。</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21747_true" name="judge_21747" value="true">
                            <label for="judge_21747_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21747_false" name="judge_21747" value="false">
                            <label for="judge_21747_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">3. HTTP是一种无状态协议，每个请求之间相互独立，不能保持会话状态</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21750_true" name="judge_21750" value="true">
                            <label for="judge_21750_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21750_false" name="judge_21750" value="false">
                            <label for="judge_21750_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">4. 黑客在入侵了一个网站后，通常会将WebShell后门文件与网站服务器Web目录下正常的网页文件混在一起，然后就可以使用浏览器来访问后门，得到一个命令执行环境，以达到控制网站服务器的目的。</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21745_true" name="judge_21745" value="true">
                            <label for="judge_21745_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21745_false" name="judge_21745" value="false">
                            <label for="judge_21745_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">5. 网络相关行业组织按照章程，加强行业自律，制定网络安全行为规范，指导会员加强网络安全保护，提高网络安全保护水平，促进行业健康发展。</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21101_true" name="judge_21101" value="true">
                            <label for="judge_21101_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21101_false" name="judge_21101" value="false">
                            <label for="judge_21101_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">6. 网络运营者可以将经过处理无法识别特定个人且不能复原的个人信息向他人提供。</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21103_true" name="judge_21103" value="true">
                            <label for="judge_21103_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21103_false" name="judge_21103" value="false">
                            <label for="judge_21103_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">7. 仿冒的网站很容易辨别，这种说法是否正确？</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21540_true" name="judge_21540" value="true">
                            <label for="judge_21540_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21540_false" name="judge_21540" value="false">
                            <label for="judge_21540_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">8. 个人信息保护主体有权要求个人信息控制者删除其个人信息等</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21740_true" name="judge_21740" value="true">
                            <label for="judge_21740_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21740_false" name="judge_21740" value="false">
                            <label for="judge_21740_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">9. 收到举报的部门但不属于本部门职责的,应及时向上级汇报</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21114_true" name="judge_21114" value="true">
                            <label for="judge_21114_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21114_false" name="judge_21114" value="false">
                            <label for="judge_21114_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">10. 在中华人民共和国境内建设、运营、维护和使用网络，以及网络安全的监督管理，适用《中华人民共和国网络安全法》。</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21100_true" name="judge_21100" value="true">
                            <label for="judge_21100_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21100_false" name="judge_21100" value="false">
                            <label for="judge_21100_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">11. 路由器只能在同一子网内进行数据包的转发，不能跨越不同子网进行通信</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21744_true" name="judge_21744" value="true">
                            <label for="judge_21744_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21744_false" name="judge_21744" value="false">
                            <label for="judge_21744_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">12. 在使用电脑或者手机上网时，没有感觉到任何的异常，这是否说明是100%的安全了？</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21522_true" name="judge_21522" value="true">
                            <label for="judge_21522_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21522_false" name="judge_21522" value="false">
                            <label for="judge_21522_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">13. 最近流行的二维码，扫一下就可以浏览网站是否有安全风险？</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21519_true" name="judge_21519" value="true">
                            <label for="judge_21519_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21519_false" name="judge_21519" value="false">
                            <label for="judge_21519_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">14. 对他人网络侵入、干扰和窃取网络数据都是违法的</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21746_true" name="judge_21746" value="true">
                            <label for="judge_21746_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21746_false" name="judge_21746" value="false">
                            <label for="judge_21746_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">15. 网络运营者为用户办理网络接入、域名注册服务,办理固定电话、移动电话等入网手续,或者为用户提供信息发布、即时通讯等服务,在与用户签订协议或者确认提供服务时,根据情况要求用户提供真实身份信息</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21116_true" name="judge_21116" value="true">
                            <label for="judge_21116_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21116_false" name="judge_21116" value="false">
                            <label for="judge_21116_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">16. 有网络安全法规定的违法行为的，依照有关法律、行政法规的规定记入信用档案，并予以公</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21106_true" name="judge_21106" value="true">
                            <label for="judge_21106_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21106_false" name="judge_21106" value="false">
                            <label for="judge_21106_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">17. 明知他人从事危害网络安全的活动，不得为其提供技术支持、广告推广和支付结算等帮助</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21118_true" name="judge_21118" value="true">
                            <label for="judge_21118_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21118_false" name="judge_21118" value="false">
                            <label for="judge_21118_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">18. 网信部门和有关部门在履行网络安全保护职责中获取的信息，不得用于维护网络安全的需要，不得用于其他用途。</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21110_true" name="judge_21110" value="true">
                            <label for="judge_21110_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21110_false" name="judge_21110" value="false">
                            <label for="judge_21110_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">19. 个人信息保护主体应当定期公开其收集、使用个人信息的规则和方式</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21743_true" name="judge_21743" value="true">
                            <label for="judge_21743_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21743_false" name="judge_21743" value="false">
                            <label for="judge_21743_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">20. 网络运营者设置恶意程序的，有关主管部门可以责令改正，给予警告；拒拒不改正或者导致危害网络安全等后果的，可以罚款。</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21104_true" name="judge_21104" value="true">
                            <label for="judge_21104_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21104_false" name="judge_21104" value="false">
                            <label for="judge_21104_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">21. 违反《网络安全法》规定，受到治安管理处罚的人员，十年内不得从事网络安全管理和网络运营关键岗位的工作；受到刑事处罚的人员，
终身不得从事网络安全管理和网络运营关键岗位的工作。</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21105_true" name="judge_21105" value="true">
                            <label for="judge_21105_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21105_false" name="judge_21105" value="false">
                            <label for="judge_21105_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">22. 《中华人民共和国网络安全法》是从2017年5月1日起施行的。</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21109_true" name="judge_21109" value="true">
                            <label for="judge_21109_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21109_false" name="judge_21109" value="false">
                            <label for="judge_21109_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">23. 任何个人和组织有权对危害网络安全的行为向国家网络安全局举报</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21112_true" name="judge_21112" value="true">
                            <label for="judge_21112_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21112_false" name="judge_21112" value="false">
                            <label for="judge_21112_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">24. 网络运营者不得收集与其提供的服务无关的个人信息，不得违反法律、行政法规的规定和双方的约定收集、使用个人信息，并应当依照法律、行政法规的规定和与用户的约定，处理其保存的个人信息。</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21102_true" name="judge_21102" value="true">
                            <label for="judge_21102_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21102_false" name="judge_21102" value="false">
                            <label for="judge_21102_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">25. 违反网络完全法的规定，给他人造成损害的，依法承担民事责任；构成违反治安管理行为的，依法给予治安管理处罚；构成犯罪的，依法追究刑事责任。</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21107_true" name="judge_21107" value="true">
                            <label for="judge_21107_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21107_false" name="judge_21107" value="false">
                            <label for="judge_21107_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">26. 登陆正规的合法的网站是否也存在网络安全风险？ </div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21520_true" name="judge_21520" value="true">
                            <label for="judge_21520_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21520_false" name="judge_21520" value="false">
                            <label for="judge_21520_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">27. 窃取或者以其他非法方式获取、非法出售或者非法向他人提供个人信息，尚不构成犯罪的，由公安机关没收违法所得，并处违法所得一倍以上十倍以下罚款，没有违法所得的，处一百万元以下罚款</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21748_true" name="judge_21748" value="true">
                            <label for="judge_21748_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21748_false" name="judge_21748" value="false">
                            <label for="judge_21748_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">28. HTTP协议无法验证通信方身份，任何人都可以伪造虚假服务器欺骗用户，实现“钓鱼欺诈”，用户无法察觉</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21751_true" name="judge_21751" value="true">
                            <label for="judge_21751_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21751_false" name="judge_21751" value="false">
                            <label for="judge_21751_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">29. 国家政务网站的运营并不适用《网络安全法》中关于网络运营者的规定</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21119_true" name="judge_21119" value="true">
                            <label for="judge_21119_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21119_false" name="judge_21119" value="false">
                            <label for="judge_21119_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">30. 网络空间主权是国家主权在网络空间的体现和延伸</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21113_true" name="judge_21113" value="true">
                            <label for="judge_21113_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21113_false" name="judge_21113" value="false">
                            <label for="judge_21113_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">31. 苹果产品是否会受到病毒和线上攻击？</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21523_true" name="judge_21523" value="true">
                            <label for="judge_21523_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21523_false" name="judge_21523" value="false">
                            <label for="judge_21523_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">32. 有关部门应当对举报人的相关信息予以保密,保护举报人的合法权益</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21115_true" name="judge_21115" value="true">
                            <label for="judge_21115_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21115_false" name="judge_21115" value="false">
                            <label for="judge_21115_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">33. 网络运营者开展经营和服务活动，可以遵守法律、行政法规，尊重社会公德，遵守商业道德，诚实信用，履行网络安全保护义务，接受政府和社会的监督，承担社会责任。</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21111_true" name="judge_21111" value="true">
                            <label for="judge_21111_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21111_false" name="judge_21111" value="false">
                            <label for="judge_21111_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">34. 内网中存在未及时更新的系统漏洞是黑客进行内网攻击的主要入口之一</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21749_true" name="judge_21749" value="true">
                            <label for="judge_21749_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21749_false" name="judge_21749" value="false">
                            <label for="judge_21749_false">错误</label>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-title">35. 网络，是指由计算机或者其他信息终端及相关设备组成的按照一定的规则和程序对信息进行收集、存储、传输、交换、处理的系统。</div>
                    <div class="options">
                        <div class="option">
                            <input type="radio" id="judge_21108_true" name="judge_21108" value="true">
                            <label for="judge_21108_true">正确</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="judge_21108_false" name="judge_21108" value="false">
                            <label for="judge_21108_false">错误</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2 class="section-title">二、单选题</h2>

                <div class="question">
                    <div class="question-title">1. 办公系统的应急演练应至少多久进行一次？</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21581_a" name="single_21581" value="a">
                            <label for="single_21581_a">A. 每年</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21581_b" name="single_21581" value="b">
                            <label for="single_21581_b">B. 2年</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21581_c" name="single_21581" value="c">
                            <label for="single_21581_c">C. 5年</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21581_d" name="single_21581" value="d">
                            <label for="single_21581_d">D. 出事就不用演练</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">2. 网上看到二手iphone5，只用了半个月，才500元，对方还留下了QQ号</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21494_a" name="single_21494" value="a">
                            <label for="single_21494_a">A. 机不可失，失不再来</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21494_b" name="single_21494" value="b">
                            <label for="single_21494_b">B. 要看看实物照片，看到底够不够新</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21494_c" name="single_21494" value="c">
                            <label for="single_21494_c">C. 要找对方要发票，方便售后保障</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21494_d" name="single_21494" value="d">
                            <label for="single_21494_d">D. 价格太低了，很可能会上当</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">3. 组织在建立安全管理体系过程中，首先应该建立的是以下哪一类文档？</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21250_a" name="single_21250" value="a">
                            <label for="single_21250_a">A. 指导方针（guideline）</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21250_b" name="single_21250" value="b">
                            <label for="single_21250_b">B. 标准（standard）</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21250_c" name="single_21250" value="c">
                            <label for="single_21250_c">C. 规程（procedure）</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21250_d" name="single_21250" value="d">
                            <label for="single_21250_d">D. 策略（policy）</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">4. 国家（ ）关键信息基础设施以外的网络运营者自愿参与关键信息基础设施保护体系。</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_22000_a" name="single_22000" value="a">
                            <label for="single_22000_a">A. 支持</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_22000_b" name="single_22000" value="b">
                            <label for="single_22000_b">B. 鼓励</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_22000_c" name="single_22000" value="c">
                            <label for="single_22000_c">C. 引导</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_22000_d" name="single_22000" value="d">
                            <label for="single_22000_d">D. 推动</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">5. 网络安全事件发生的风险增大时,省级以上人民政府有关部门应当按照规定的(),并根据网络安全风险的特点和可能造成的危害,采取下列措施:(一)要求有关部门、机构和人员及时收集、报告有关信息,加强对网络安全风险的监测;(二)组织有关部门、机构和专业人员,对网络安全风险信息进行分析评估,预测事件发生的可能性、影响范围和危害程度;(三)向社会发布网络安全风险预警,发布避免、减轻危害的措施。</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21343_a" name="single_21343" value="a">
                            <label for="single_21343_a">A. 权限</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21343_b" name="single_21343" value="b">
                            <label for="single_21343_b">B. 程序</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21343_c" name="single_21343" value="c">
                            <label for="single_21343_c">C. 权限和程序</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21343_d" name="single_21343" value="d">
                            <label for="single_21343_d">D. 流程</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">6. 在网络访问过程中，为了防御网络监听，最常用的方法是()</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_22031_a" name="single_22031" value="a">
                            <label for="single_22031_a">A. 采用物理传输(非网络)</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_22031_b" name="single_22031" value="b">
                            <label for="single_22031_b">B. 对信息传输进行加密</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_22031_c" name="single_22031" value="c">
                            <label for="single_22031_c">C. 进行网络伪装</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_22031_d" name="single_22031" value="d">
                            <label for="single_22031_d">D. 进行网络压制</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">7. 老王是某政府信息中心主任。以下哪项项目是符合《保守国家秘密法》要求的（  ）</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21763_a" name="single_21763" value="a">
                            <label for="single_21763_a">A. 老王安排下属小李将损害的涉密计算机的某国外品牌硬盘送到该品牌中国区维修中心修理</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21763_b" name="single_21763" value="b">
                            <label for="single_21763_b">B. 老王要求下属小张把中心所有计算机贴上密级标志</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21763_c" name="single_21763" value="c">
                            <label for="single_21763_c">C. 老王每天晚上12点将涉密计算机连接上互联网更新杀毒软件病毒库</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21763_d" name="single_21763" value="d">
                            <label for="single_21763_d">D. 老王提出对加密机和红黑电源智能插座应该与涉密信息系统三同步，合格后方可投入使用</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">8. 下列关于口令持有人保证口令保密性的做法正确的是()</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_22044_a" name="single_22044" value="a">
                            <label for="single_22044_a">A. 将口令记录的笔记本中</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_22044_b" name="single_22044" value="b">
                            <label for="single_22044_b">B. 将口令贴在计算机机箱或终端屏幕上</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_22044_c" name="single_22044" value="c">
                            <label for="single_22044_c">C. 将计算机系统用户口令借给他人使用</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_22044_d" name="single_22044" value="d">
                            <label for="single_22044_d">D. 一旦发现或怀疑计算机系统用户口令泄露，立即更换</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">9. 哪种建筑结构／材质对无线信号的损耗最大</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21689_a" name="single_21689" value="a">
                            <label for="single_21689_a">A. 木板</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21689_b" name="single_21689" value="b">
                            <label for="single_21689_b">B. 玻璃</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21689_c" name="single_21689" value="c">
                            <label for="single_21689_c">C. 水泥墙</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21689_d" name="single_21689" value="d">
                            <label for="single_21689_d">D. 砖墙</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">10. 2014年，首届互联网世界大会在浙江的乌镇举行，互联网的影响日益深化，其带来的最大挑战是</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21248_a" name="single_21248" value="a">
                            <label for="single_21248_a">A. 网络立法问题</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21248_b" name="single_21248" value="b">
                            <label for="single_21248_b">B. 网络安全问题</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21248_c" name="single_21248" value="c">
                            <label for="single_21248_c">C. 网络宣传问题</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21248_d" name="single_21248" value="d">
                            <label for="single_21248_d">D. 全球网络连接问题</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">11. 假设某事件波及一个或多个省市的大部分地区，极大威胁国家安全，引起社会动荡，对经济建设有极其恶劣的负面影响，或者严重损害公众利益。则该事件属于（）</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21158_a" name="single_21158" value="a">
                            <label for="single_21158_a">A. 特别重大事件</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21158_b" name="single_21158" value="b">
                            <label for="single_21158_b">B. 重大事件</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21158_c" name="single_21158" value="c">
                            <label for="single_21158_c">C. 较大事件</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21158_d" name="single_21158" value="d">
                            <label for="single_21158_d">D. 一般事件</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">12. 以下哪个动作不属于安全测试中的信息收集环节：</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21838_a" name="single_21838" value="a">
                            <label for="single_21838_a">A. 通过google寻找相关域名的信息</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21838_b" name="single_21838" value="b">
                            <label for="single_21838_b">B. 通过whois查询相关域名的备案信息</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21838_c" name="single_21838" value="c">
                            <label for="single_21838_c">C. 通过DAVExplorer查询相关主机是否开启了webdav</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21838_d" name="single_21838" value="d">
                            <label for="single_21838_d">D. 通过Httprint查看主机使用了哪种中间件</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">13. 以下那款工具不属于商用web扫描器：</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21840_a" name="single_21840" value="a">
                            <label for="single_21840_a">A. AWVS</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21840_b" name="single_21840" value="b">
                            <label for="single_21840_b">B. IBMAppscan</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21840_c" name="single_21840" value="c">
                            <label for="single_21840_c">C. N-stalker</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21840_d" name="single_21840" value="d">
                            <label for="single_21840_d">D. SQLMAP</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">14. 小刘在某电器城购买了一台冰箱，并留下了个人姓名、电话和电子邮件地址等信息，第二天他收到了一封来自电器城提示他中奖的邮件，查看该邮件后他按照提示缴纳中奖税款后并没有得到中奖奖金，再打电话询问电器城才得知电器城并没有举办中奖活动。根据上面的描述，由此可以推断的是？</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21369_a" name="single_21369" value="a">
                            <label for="single_21369_a">A. 小刘在电器城登记个人信息时，使用了加密手段</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21369_b" name="single_21369" value="b">
                            <label for="single_21369_b">B. 小刘遭受了钓鱼邮件攻击，钱被骗走了</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21369_c" name="single_21369" value="c">
                            <label for="single_21369_c">C. 小刘的计算机中了木马，被远程控制</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21369_d" name="single_21369" value="d">
                            <label for="single_21369_d">D. 小刘购买的冰箱是智能冰箱，可以连网</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">15. 单位的管理者对每个员工的“职责分工”属于以下那一类控制措施？</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21246_a" name="single_21246" value="a">
                            <label for="single_21246_a">A. 预防性控制措施</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21246_b" name="single_21246" value="b">
                            <label for="single_21246_b">B. 检查性控制措施</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21246_c" name="single_21246" value="c">
                            <label for="single_21246_c">C. 纠正性控制措施</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21246_d" name="single_21246" value="d">
                            <label for="single_21246_d">D. 评估性控制措施</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">16. 以下哪个生活习惯属有助于保护用户个人信息()</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_22056_a" name="single_22056" value="a">
                            <label for="single_22056_a">A. 银行卡充值后的回单随手扔掉</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_22056_b" name="single_22056" value="b">
                            <label for="single_22056_b">B. 在网站上随意下载免费和破解软件</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_22056_c" name="single_22056" value="c">
                            <label for="single_22056_c">C. 在手机和电脑上安装防偷窥的保护膜</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_22056_d" name="single_22056" value="d">
                            <label for="single_22056_d">D. 看见二维码，先扫了再说</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">17. 李同学浏览网页时弹出“新版游戏，免费玩，点击就送大礼包”的广告，李同学点了之后发现是个网页游戏，提示：“请安装插件”，请问，这种情况李同学应该怎么办最合适？（）</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21363_a" name="single_21363" value="a">
                            <label for="single_21363_a">A. 为了领取大礼包，安装插件之后玩游戏</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21363_b" name="single_21363" value="b">
                            <label for="single_21363_b">B. 网页游戏一般是不需要安装插件的，这种情况骗局的可能性非常大，不建议打开</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21363_c" name="single_21363" value="c">
                            <label for="single_21363_c">C. 询问朋友是否玩过这个游戏，朋友如果说玩过，那应该没事。</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21363_d" name="single_21363" value="d">
                            <label for="single_21363_d">D. 先将操作系统做备份，如果安装插件之后有异常，大不了恢复系统</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">18. 在网络安全预警分级中，与国家安全关系一般，或与经济建设、社会生活关系密切的系统属于（）</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21164_a" name="single_21164" value="a">
                            <label for="single_21164_a">A. 一般重要的保护对象</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21164_b" name="single_21164" value="b">
                            <label for="single_21164_b">B. 重要的保护对象</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21164_c" name="single_21164" value="c">
                            <label for="single_21164_c">C. 特别重要的保护对象</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21164_d" name="single_21164" value="d">
                            <label for="single_21164_d">D. 不需要保护的对象</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">19. 国家鼓励个人信息处理者采用（ ）的数据保护技术和手段，提升个人信息保护能力。</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21925_a" name="single_21925" value="a">
                            <label for="single_21925_a">A. 先进</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21925_b" name="single_21925" value="b">
                            <label for="single_21925_b">B. 落后</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21925_c" name="single_21925" value="c">
                            <label for="single_21925_c">C. 传统</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21925_d" name="single_21925" value="d">
                            <label for="single_21925_d">D. 单一</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">20. 网上购物，下述支付方式中，最安全的是</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21484_a" name="single_21484" value="a">
                            <label for="single_21484_a">A. 使用第三方支付平台在正规购物网站上进行支付</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21484_b" name="single_21484" value="b">
                            <label for="single_21484_b">B. 使用第三方支付平台在在钓鱼网站上进行支付</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21484_c" name="single_21484" value="c">
                            <label for="single_21484_c">C. 向个人账户转账</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21484_d" name="single_21484" value="d">
                            <label for="single_21484_d">D. 向企业账户转账</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">21. 在Unix系统中，用户的登录或退出错误日志应该保存在以下哪个相对路径中？</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21614_a" name="single_21614" value="a">
                            <label for="single_21614_a">A. /etc</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21614_b" name="single_21614" value="b">
                            <label for="single_21614_b">B. /home</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21614_c" name="single_21614" value="c">
                            <label for="single_21614_c">C. /lib</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21614_d" name="single_21614" value="d">
                            <label for="single_21614_d">D. /dev</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">22. 国家网信部门和有关部门依法履行网络信息安全监督管理职责,发现法律、行政法规禁止发布或者传输的信息的,应当要求()停止传输,采取消除等处置措施,保存有关记录;对来源于中华人民共和国境外的上述信息,应当通知有关机构采取技术措施和其他必要措施阻断传播。</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21340_a" name="single_21340" value="a">
                            <label for="single_21340_a">A. 网络运营者</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21340_b" name="single_21340" value="b">
                            <label for="single_21340_b">B. 网络使用者</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21340_c" name="single_21340" value="c">
                            <label for="single_21340_c">C. 网络发布者</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21340_d" name="single_21340" value="d">
                            <label for="single_21340_d">D. 网络造谣者</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">23. 《数据安全法》规定，关系国家安全、国民经济命脉、重要民生、重大公共利益等数据属于()。</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_22030_a" name="single_22030" value="a">
                            <label for="single_22030_a">A. 国家保密数据</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_22030_b" name="single_22030" value="b">
                            <label for="single_22030_b">B. 国家重要数据</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_22030_c" name="single_22030" value="c">
                            <label for="single_22030_c">C. 普通数据</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_22030_d" name="single_22030" value="d">
                            <label for="single_22030_d">D. 国家核心数据</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">24. 当攻击者伪造源IP地址，并将其以ICMP Echo广播包的形式发送出去，利用返回的reply报文，从而威胁受害者的攻击方式被称为</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21669_a" name="single_21669" value="a">
                            <label for="single_21669_a">A. SYN flood攻击</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21669_b" name="single_21669" value="b">
                            <label for="single_21669_b">B. Smurf攻击</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21669_c" name="single_21669" value="c">
                            <label for="single_21669_c">C. Ping of Death攻击</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21669_d" name="single_21669" value="d">
                            <label for="single_21669_d">D. 缓冲区溢出攻击</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">25. 蠕虫和病毒的最大区别是（）</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21573_a" name="single_21573" value="a">
                            <label for="single_21573_a">A. 自我复制</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21573_b" name="single_21573" value="b">
                            <label for="single_21573_b">B. 主动传播</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21573_c" name="single_21573" value="c">
                            <label for="single_21573_c">C. 是否需要人机交互</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21573_d" name="single_21573" value="d">
                            <label for="single_21573_d">D. 多感染途径</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">26. TCP/IP提供（  ）的链接机制，将数据应该如何封装、定址、传输、路由以及在目的地如何接收，都加以标准化。</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21729_a" name="single_21729" value="a">
                            <label for="single_21729_a">A. 单点</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21729_b" name="single_21729" value="b">
                            <label for="single_21729_b">B. 多点</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21729_c" name="single_21729" value="c">
                            <label for="single_21729_c">C. 点对点</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21729_d" name="single_21729" value="d">
                            <label for="single_21729_d">D. 点对多点</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">27. 以下哪个命令可以解析域名与IP的对应关系？</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21619_a" name="single_21619" value="a">
                            <label for="single_21619_a">A. Tracert</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21619_b" name="single_21619" value="b">
                            <label for="single_21619_b">B. nslookup</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21619_c" name="single_21619" value="c">
                            <label for="single_21619_c">C. telnet</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21619_d" name="single_21619" value="d">
                            <label for="single_21619_d">D. whoami</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">28. 下面不能防范电子邮件攻击的是（）</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21554_a" name="single_21554" value="a">
                            <label for="single_21554_a">A. 采用FoxMail</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21554_b" name="single_21554" value="b">
                            <label for="single_21554_b">B. 采用电子邮件安全加密软件</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21554_c" name="single_21554" value="c">
                            <label for="single_21554_c">C. 采用Outlook Express</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21554_d" name="single_21554" value="d">
                            <label for="single_21554_d">D. 安装入侵检测工具</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">29. 关于计算机病毒的叙述中，错误的是</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21511_a" name="single_21511" value="a">
                            <label for="single_21511_a">A. 一台微机用反病毒软件清除过病毒后，就不会再被传染新的病毒</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21511_b" name="single_21511" value="b">
                            <label for="single_21511_b">B. 计算机病毒也是一种程序</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21511_c" name="single_21511" value="c">
                            <label for="single_21511_c">C. 病毒程序只有在计算机运行时才会复制并传染</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21511_d" name="single_21511" value="d">
                            <label for="single_21511_d">D. 单机状态的微机，磁盘是传染病毒的主要媒介</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">30. 一家B2C电子商务网站的信息安全程序要求能够监测和预防黑客的活动，一有可疑行为即警示系统管理员。下面的哪个系统组件可以实现这个目标？()</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21273_a" name="single_21273" value="a">
                            <label for="single_21273_a">A. 入侵监测系统（IDS）</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21273_b" name="single_21273" value="b">
                            <label for="single_21273_b">B. 防火墙</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21273_c" name="single_21273" value="c">
                            <label for="single_21273_c">C. 路由器</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21273_d" name="single_21273" value="d">
                            <label for="single_21273_d">D. 不对称加密</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">31. 以下哪种攻击方法是利用TCP连接三次握手弱点进行的(  )</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21607_a" name="single_21607" value="a">
                            <label for="single_21607_a">A. SYN Flood</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21607_b" name="single_21607" value="b">
                            <label for="single_21607_b">B. 嗅探</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21607_c" name="single_21607" value="c">
                            <label for="single_21607_c">C. 会话劫持</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21607_d" name="single_21607" value="d">
                            <label for="single_21607_d">D. SQL注入</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">32. 任何个人和组织不得从事非法侵入他人网络、干扰他人网络正常功能、窃取()等危害网络安全的活动;</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21307_a" name="single_21307" value="a">
                            <label for="single_21307_a">A. 机密</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21307_b" name="single_21307" value="b">
                            <label for="single_21307_b">B. 网络数据</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21307_c" name="single_21307" value="c">
                            <label for="single_21307_c">C. 信息</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21307_d" name="single_21307" value="d">
                            <label for="single_21307_d">D. 资料</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">33. 以下关于使用APP的习惯不正确的是（）</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21809_a" name="single_21809" value="a">
                            <label for="single_21809_a">A. 不使用强制收集无关个人信息的 APP</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21809_b" name="single_21809" value="b">
                            <label for="single_21809_b">B. 为了获取更多积分，填写真实姓名.出生日期.手机号码等所有的信息</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21809_c" name="single_21809" value="c">
                            <label for="single_21809_c">C. 谨慎使用各种需要填写个人信息的问卷调查的 App</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21809_d" name="single_21809" value="d">
                            <label for="single_21809_d">D. 加强对不良 APP 的辨识能力，不轻易被赚钱等噱头迷惑</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">34. 我看到一个股票网站，很多知名的金融学家都在网站上做股票预测，名气越大的收费越高。</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21496_a" name="single_21496" value="a">
                            <label for="single_21496_a">A. 骗人的钓鱼网站，相信就一定会上当</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21496_b" name="single_21496" value="b">
                            <label for="single_21496_b">B. 现在名人也都下海赚钱，但水平参差不齐</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21496_c" name="single_21496" value="c">
                            <label for="single_21496_c">C. 可以试试，预测的准就长期投资</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21496_d" name="single_21496" value="d">
                            <label for="single_21496_d">D. 有名人在，一定错不了</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">35. 从攻击方式区分攻击类型，可分为被动攻击和主动攻击。被动攻击难以（   ），然而（   ）这些攻击是可行的；主动攻击难以（   ），然而（    ）这些攻击是可行的。</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21848_a" name="single_21848" value="a">
                            <label for="single_21848_a">A. 阻止，检测，阻止，检测</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21848_b" name="single_21848" value="b">
                            <label for="single_21848_b">B. 检测，阻止，检测，阻止</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21848_c" name="single_21848" value="c">
                            <label for="single_21848_c">C. 检测，阻止，阻止，检测</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21848_d" name="single_21848" value="d">
                            <label for="single_21848_d">D. 阻止，检测，检测，阻止</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">36. （）是通过进一步分析信息系统的整体安全性，对信息系统实施的综合安全测评</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21148_a" name="single_21148" value="a">
                            <label for="single_21148_a">A. 单元测评</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21148_b" name="single_21148" value="b">
                            <label for="single_21148_b">B. 整体测评</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21148_c" name="single_21148" value="c">
                            <label for="single_21148_c">C. 黑盒测评</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21148_d" name="single_21148" value="d">
                            <label for="single_21148_d">D. 白盒测评</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">37. VPN的加密手段为（    ）。</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21963_a" name="single_21963" value="a">
                            <label for="single_21963_a">A. 具有加密功能的防火墙</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21963_b" name="single_21963" value="b">
                            <label for="single_21963_b">B. 具有加密功能的路由器</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21963_c" name="single_21963" value="c">
                            <label for="single_21963_c">C. VPN内的各台主机对各自的信息进行相应的加密</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21963_d" name="single_21963" value="d">
                            <label for="single_21963_d">D. 单独的加密设备</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">38. Linux中，以下哪个服务不能独立启动？（）</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21786_a" name="single_21786" value="a">
                            <label for="single_21786_a">A. apache服务</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21786_b" name="single_21786" value="b">
                            <label for="single_21786_b">B. FTP服务</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21786_c" name="single_21786" value="c">
                            <label for="single_21786_c">C. Samba服务</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21786_d" name="single_21786" value="d">
                            <label for="single_21786_d">D. 基于xinetd的服务</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">39. 为了防止各种各样的病毒对计算机系统造成危害，可以在计算机上安装防病毒软件，并注意及时 （），以保证能防止和查杀新近出现的病毒。</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21462_a" name="single_21462" value="a">
                            <label for="single_21462_a">A. 分析</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21462_b" name="single_21462" value="b">
                            <label for="single_21462_b">B. 升级</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21462_c" name="single_21462" value="c">
                            <label for="single_21462_c">C. 检查</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21462_d" name="single_21462" value="d">
                            <label for="single_21462_d">D. 更换</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">40. 对于Windows系统的IIS的认证及其帐号，下面说法错误的是：</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21952_a" name="single_21952" value="a">
                            <label for="single_21952_a">A. IUSR_computer为内置帐号，缺省属于administrators组</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21952_b" name="single_21952" value="b">
                            <label for="single_21952_b">B. IIS支持域帐号的访问</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21952_c" name="single_21952" value="c">
                            <label for="single_21952_c">C. 采用基本认证方式时，用户名和密码是明文发送的</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21952_d" name="single_21952" value="d">
                            <label for="single_21952_d">D. 采用windows集成认证方式时，密码和帐号信息将以加密的方式传输</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">41. 下面哪个网址是真正的淘宝官网</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21487_a" name="single_21487" value="a">
                            <label for="single_21487_a">A. http://www.ta0ba0.com/</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21487_b" name="single_21487" value="b">
                            <label for="single_21487_b">B. http://www.taobac.com/</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21487_c" name="single_21487" value="c">
                            <label for="single_21487_c">C. http://%39%6D%70%2E%63%6F%6D/BOhm6?http://item.taobao.com</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21487_d" name="single_21487" value="d">
                            <label for="single_21487_d">D. http://www.taobao.com/</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">42. 为应对信息安全威胁的快速增长，企业最有效的办法是：</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21276_a" name="single_21276" value="a">
                            <label for="single_21276_a">A. 建立在潜在事件发生前进行处理的计划</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21276_b" name="single_21276" value="b">
                            <label for="single_21276_b">B. 建立一支专职的高技术安全事件应急小组</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21276_c" name="single_21276" value="c">
                            <label for="single_21276_c">C. 根据事件对企业实际的影响采取不同的应对措施</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21276_d" name="single_21276" value="d">
                            <label for="single_21276_d">D. 购买第三方安全事件响应服务以应对新的威胁</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">43. 网络运营者为用户办理网络接入、域名注册服务，办理固定电话、移动电话等入网手续，或者为用户提供信息发布、即时通讯等服务，应当要求用户提供（ ）。</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21999_a" name="single_21999" value="a">
                            <label for="single_21999_a">A. 联系方式</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21999_b" name="single_21999" value="b">
                            <label for="single_21999_b">B. 真实身份信息</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21999_c" name="single_21999" value="c">
                            <label for="single_21999_c">C. 家庭住址</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21999_d" name="single_21999" value="d">
                            <label for="single_21999_d">D. 工作单位</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">44. 对于非常重要的敏感数据因使用（      ）算法加密。</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21639_a" name="single_21639" value="a">
                            <label for="single_21639_a">A. HASH</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21639_b" name="single_21639" value="b">
                            <label for="single_21639_b">B. MD5</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21639_c" name="single_21639" value="c">
                            <label for="single_21639_c">C. AES</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">45. SQL 杀手蠕虫病毒发作的特征是什么()</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21825_a" name="single_21825" value="a">
                            <label for="single_21825_a">A. 大量消耗网络带宽</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21825_b" name="single_21825" value="b">
                            <label for="single_21825_b">B. 攻击个人PC终端</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21825_c" name="single_21825" value="c">
                            <label for="single_21825_c">C. 破坏PC游戏程序</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21825_d" name="single_21825" value="d">
                            <label for="single_21825_d">D. 攻击手机网络</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">46. 关于发送电子邮件，下列说法中正确的是（ ）。</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21535_a" name="single_21535" value="a">
                            <label for="single_21535_a">A. 你必须先接入Internet，别人才可以给你发送电子邮件</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21535_b" name="single_21535" value="b">
                            <label for="single_21535_b">B. 你只有打开了自己的计算机，别人才可以给你发送电子邮件</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21535_c" name="single_21535" value="c">
                            <label for="single_21535_c">C. 只要有E-M il地址，别人就可以给你发送电子邮件</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21535_d" name="single_21535" value="d">
                            <label for="single_21535_d">D. 别人只要接入了Internet，就可以给你发送电子邮件</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">47. 关于安全等级保护测评力度，下列说法错误的是（）</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21143_a" name="single_21143" value="a">
                            <label for="single_21143_a">A. 投入越多，测评力度就越强，测评就越有保证</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21143_b" name="single_21143" value="b">
                            <label for="single_21143_b">B. 测评广度越大，测评实施的范围越大，测评实施包含的测评对象就越多；测评深度越深，越需要在细节上展开，测评就越严格，因此，就越需要更多的投入</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21143_c" name="single_21143" value="c">
                            <label for="single_21143_c">C. 测评力度是在测评过程中实施测评工作的力度，反映测评的广度和深度，体现为测评工作的实际投入程度</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21143_d" name="single_21143" value="d">
                            <label for="single_21143_d">D. 测评的广度和深度落实到运行、检查、验收三种不同的测评方法上，能体现出测评实施过程中各方法投入程度的不同</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">48. 下列现象中，不能作为判断是否受到黑客攻击的依据是</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21684_a" name="single_21684" value="a">
                            <label for="single_21684_a">A. “系统自动升级”</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21684_b" name="single_21684" value="b">
                            <label for="single_21684_b">B. “系统自动重启”</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21684_c" name="single_21684" value="c">
                            <label for="single_21684_c">C. “磁盘被共享”</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21684_d" name="single_21684" value="d">
                            <label for="single_21684_d">D. “文件被篡改”</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">49. SDN的典型架构有几层组成</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21685_a" name="single_21685" value="a">
                            <label for="single_21685_a">A. 1</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21685_b" name="single_21685" value="b">
                            <label for="single_21685_b">B. 2</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21685_c" name="single_21685" value="c">
                            <label for="single_21685_c">C. 3</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21685_d" name="single_21685" value="d">
                            <label for="single_21685_d">D. 4</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">50. 没有自拍，也没有视频聊天，但电脑摄像头的灯总是亮着，这是什么原因？</div>
                    <div class="options">

                        <div class="option">
                            <input type="radio" id="single_21370_a" name="single_21370" value="a">
                            <label for="single_21370_a">A. 可能中了木马，正在被黑客偷窥</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21370_b" name="single_21370" value="b">
                            <label for="single_21370_b">B. 电脑坏了</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21370_c" name="single_21370" value="c">
                            <label for="single_21370_c">C. 本来就该亮着</label>
                        </div>

                        <div class="option">
                            <input type="radio" id="single_21370_d" name="single_21370" value="d">
                            <label for="single_21370_d">D. 摄像头坏了</label>
                        </div>

                    </div>
                </div>
            </div>

            <div class="section">
                <h2 class="section-title">三、多选题</h2>

                <div class="question">
                    <div class="question-title">1. 任何个人和组织应当对其使用网络的行为负责，不得设立用于实施诈骗，传授犯罪方法，()等违法犯罪活动的网站、通讯</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21201_a" name="multi_21201" value="a">
                            <label for="multi_21201_a">A. 传授养身秘诀</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21201_b" name="multi_21201" value="b">
                            <label for="multi_21201_b">B. 制作或者销售图书</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21201_c" name="multi_21201" value="c">
                            <label for="multi_21201_c">C. 制作或者销售违禁物品</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21201_d" name="multi_21201" value="d">
                            <label for="multi_21201_d">D. 制作或者销售管制物品</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">2. 国家实行网络安全等级保护制度。网络运营者应当按照网络安全等级保护制度的要求，履行安全保护义务，()。</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21094_a" name="multi_21094" value="a">
                            <label for="multi_21094_a">A. 保障网络免受干扰、破坏</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21094_b" name="multi_21094" value="b">
                            <label for="multi_21094_b">B. 保障网络免受未经授权的访问</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21094_c" name="multi_21094" value="c">
                            <label for="multi_21094_c">C. 防止网络数据泄露</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21094_d" name="multi_21094" value="d">
                            <label for="multi_21094_d">D. 防止网络数据被窃取、篡改</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">3.  被感染病毒后，计算机可能出现的异常现象或症状有</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21570_a" name="multi_21570" value="a">
                            <label for="multi_21570_a">A. 计算机系统出现异常死机或死机频繁</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21570_b" name="multi_21570" value="b">
                            <label for="multi_21570_b">B. 系统被非法远程控制</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21570_c" name="multi_21570" value="c">
                            <label for="multi_21570_c">C. 文件的内容和文件属性无故改变</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21570_d" name="multi_21570" value="d">
                            <label for="multi_21570_d">D. 自动发送邮件</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">4. 任何个人和组织应当对其使用网络的行为负责，不得设立用于（）违法犯罪活动的网站、通讯群组，不得利用网络发布涉及实施诈骗，制作或者销售违禁物品、管制物品以及其他违法犯罪活动的信息。</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21195_a" name="multi_21195" value="a">
                            <label for="multi_21195_a">A. 实施诈骗</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21195_b" name="multi_21195" value="b">
                            <label for="multi_21195_b">B. 制作管制物品</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21195_c" name="multi_21195" value="c">
                            <label for="multi_21195_c">C. 传授犯罪方法</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21195_d" name="multi_21195" value="d">
                            <label for="multi_21195_d">D. 销售管制物品</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">5. 网络运营者应当按照网络安全等级保护制度的要求，保障网络免受干扰、破坏或者未经授权的访问，防止网络数据泄露或者被窃取、篡改。以下哪些属于需履行的安全保护义务（）。</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21174_a" name="multi_21174" value="a">
                            <label for="multi_21174_a">A. 制定通过认证的安全管理制度和操作规程，确定网络安全负责人，落实网络安全保护责任</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21174_b" name="multi_21174" value="b">
                            <label for="multi_21174_b">B. 采取防范计算机病毒和网络攻击、网络侵入等危害网络安全行为的技术措施</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21174_c" name="multi_21174" value="c">
                            <label for="multi_21174_c">C. 采取监测、记录网络运行状态、网络安全事件的技术措施，并按照规定留存相关的网络日志不少于六个月</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21174_d" name="multi_21174" value="d">
                            <label for="multi_21174_d">D. 采取数据分类、重要数据备份和加密等措施</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">6. 网购时候要如何才能确保交易的安全？           </div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21572_a" name="multi_21572" value="a">
                            <label for="multi_21572_a">A. 在系统上安装专业的安全软件</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21572_b" name="multi_21572" value="b">
                            <label for="multi_21572_b">B. 定期进行系统安全扫描</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21572_c" name="multi_21572" value="c">
                            <label for="multi_21572_c">C. 通过安全软件鉴别网站的安全性</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21572_d" name="multi_21572" value="d">
                            <label for="multi_21572_d">D. 使用专业的网银工具</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">7. 网络安全事件发生的风险增大时，省级以上人民政府有关部门应当按照规定的权限和程序，并根据网络安全风险的特点和可能造成的危害，采取下列措施：()。</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21197_a" name="multi_21197" value="a">
                            <label for="multi_21197_a">A. 要求有关部门、机构和人员及时收集、报告有关信息，加强对网络安全风险的监测。</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21197_b" name="multi_21197" value="b">
                            <label for="multi_21197_b">B. 组织有关部门、机构和专业人员，对网络安全风险信息进行分析评估，预测事件发生的可能性、影响范围和危害程度。</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21197_c" name="multi_21197" value="c">
                            <label for="multi_21197_c">C. 向社会发布网络安全风险预警，发布避免、减轻危害的措施。</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21197_d" name="multi_21197" value="d">
                            <label for="multi_21197_d">D. 向有关部门报告</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">8. 下列关于点击手机恶意链接的后产生的恶果有</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21738_a" name="multi_21738" value="a">
                            <label for="multi_21738_a">A. 进入钓鱼网站，泄漏重要信息</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21738_b" name="multi_21738" value="b">
                            <label for="multi_21738_b">B. 掉入恶意吸费的陷阱</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21738_c" name="multi_21738" value="c">
                            <label for="multi_21738_c">C. 被强行安装恶意软件</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21738_d" name="multi_21738" value="d">
                            <label for="multi_21738_d">D. 财产损失</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">9. 《网络安全法》所指网络安全，是指通过采取必要措施，防范对网络的（）以及意外事故，使网络处于稳定可靠运行的状态，以及保障网络数据的完整性、保密性、可用性的能</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21233_a" name="multi_21233" value="a">
                            <label for="multi_21233_a">A. 攻击</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21233_b" name="multi_21233" value="b">
                            <label for="multi_21233_b">B. 侵入</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21233_c" name="multi_21233" value="c">
                            <label for="multi_21233_c">C. 干扰</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21233_d" name="multi_21233" value="d">
                            <label for="multi_21233_d">D. 破坏</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">10. 如何安全使用电子邮件？</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21377_a" name="multi_21377" value="a">
                            <label for="multi_21377_a">A. 不要随意点击不明邮件中的链接、图片、文件</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21377_b" name="multi_21377" value="b">
                            <label for="multi_21377_b">B. 当收到与个人信息和金钱相关（如中奖、集资等）的邮件时要提高警惕</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21377_c" name="multi_21377" value="c">
                            <label for="multi_21377_c">C. 只要密码强度高，可以不用设置找回密码的提示问题</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21377_d" name="multi_21377" value="d">
                            <label for="multi_21377_d">D. 使用电子邮件地址作为网站注册的用户名时，可以设置与原邮件密码相同的网站密码</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">11. 目前最好的防病毒软件能做到的是</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21565_a" name="multi_21565" value="a">
                            <label for="multi_21565_a">A. 检查计算机是否染有病毒，消除已感染的任何病毒</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21565_b" name="multi_21565" value="b">
                            <label for="multi_21565_b">B. 杜绝病毒对计算机的侵害</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21565_c" name="multi_21565" value="c">
                            <label for="multi_21565_c">C. 查出计算机已感染的已知病毒，消除其中的一部分</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21565_d" name="multi_21565" value="d">
                            <label for="multi_21565_d">D. 检查计算机是否染有已知病毒，并作相应处理</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">12. 依法负有网络安全监督管理职责的部门及其工作人员，必须对在履行职责中知悉的（）严格保密，不得泄露、出售或者非法向他人提供。</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21179_a" name="multi_21179" value="a">
                            <label for="multi_21179_a">A. 上市公司公告内容</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21179_b" name="multi_21179" value="b">
                            <label for="multi_21179_b">B. 党政机关负责人公示的个人信息</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21179_c" name="multi_21179" value="c">
                            <label for="multi_21179_c">C. 隐私</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21179_d" name="multi_21179" value="d">
                            <label for="multi_21179_d">D. 商业秘密</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">13. 网络运营者不得（）其收集的个人信息,未经被收集者同意,不得向他人提供个人信息。但是,经过处理无法识别特定个人且不能复原的</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21083_a" name="multi_21083" value="a">
                            <label for="multi_21083_a">A. 毁损</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21083_b" name="multi_21083" value="b">
                            <label for="multi_21083_b">B. 篡改</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21083_c" name="multi_21083" value="c">
                            <label for="multi_21083_c">C. 使用</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21083_d" name="multi_21083" value="d">
                            <label for="multi_21083_d">D. 泄露</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">14. 网络协议组成的三要素是（  ）。</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21741_a" name="multi_21741" value="a">
                            <label for="multi_21741_a">A. 语法</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21741_b" name="multi_21741" value="b">
                            <label for="multi_21741_b">B. 语义</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21741_c" name="multi_21741" value="c">
                            <label for="multi_21741_c">C. 语序</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21741_d" name="multi_21741" value="d">
                            <label for="multi_21741_d">D. 定时</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">15. 下列关于《网络安全法》的说法错误的有</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21408_a" name="multi_21408" value="a">
                            <label for="multi_21408_a">A. 国家规定关键信息基础设施以外的网络运营者必须参与关键信息基础设施保护体系</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21408_b" name="multi_21408" value="b">
                            <label for="multi_21408_b">B. 关键信息基础设施的运营者可自行采购网络产品和服务不通过安全审查</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21408_c" name="multi_21408" value="c">
                            <label for="multi_21408_c">C. 网络运营者应当加强对其用户发布的信息的管理，发现法律、行政法规禁止发布或者传输的信息的，应当立即向上级汇报</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21408_d" name="multi_21408" value="d">
                            <label for="multi_21408_d">D. 国家网信部门应当统筹协调有关部门加强网络安全信息收集、分析和通报工作，按照规定统一发布网络安全监测预警信息</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">16. 未按国家有关规定向社会发布系统漏洞、计算机病毒、网络攻击、网络侵入等网络安全信息的，由有关主管部门()。</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21207_a" name="multi_21207" value="a">
                            <label for="multi_21207_a">A. 给予提醒</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21207_b" name="multi_21207" value="b">
                            <label for="multi_21207_b">B. 责令改正</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21207_c" name="multi_21207" value="c">
                            <label for="multi_21207_c">C. 给予警告</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21207_d" name="multi_21207" value="d">
                            <label for="multi_21207_d">D. 给予批评</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">17. 提供电子认证服务，应当具备下列条件：</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21431_a" name="multi_21431" value="a">
                            <label for="multi_21431_a">A. 取得企业法人资格</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21431_b" name="multi_21431" value="b">
                            <label for="multi_21431_b">B. 具有与提供电子认证服务相适应的专业技术人员和管理人员</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21431_c" name="multi_21431" value="c">
                            <label for="multi_21431_c">C. 具有与提供电子认证服务相适应的资金和经营场所</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21431_d" name="multi_21431" value="d">
                            <label for="multi_21431_d">D. 具有符合国家安全标准的技术和设备</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">18. 个人信息，是指以电子或者其他方式记录的能够单独或者与其他信息结合识别自然人个人身份的各种信息，包括但不限于自然人的（）等。</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21237_a" name="multi_21237" value="a">
                            <label for="multi_21237_a">A. 姓名</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21237_b" name="multi_21237" value="b">
                            <label for="multi_21237_b">B. 出生日期</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21237_c" name="multi_21237" value="c">
                            <label for="multi_21237_c">C. 身份证件号码</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21237_d" name="multi_21237" value="d">
                            <label for="multi_21237_d">D. 个人生物识别信息</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">19. 现在的智能设备能直接收集到身体相应信息，比如我们佩戴的手环收集个人健康数据。以下哪些行为可能造成个人信息泄露？</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21367_a" name="multi_21367" value="a">
                            <label for="multi_21367_a">A. 将手环外借他人</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21367_b" name="multi_21367" value="b">
                            <label for="multi_21367_b">B. 接入陌生网络</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21367_c" name="multi_21367" value="c">
                            <label for="multi_21367_c">C. 手环电量低</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21367_d" name="multi_21367" value="d">
                            <label for="multi_21367_d">D. 分享跑步时的路径信息</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">20. 网络安全事件发生的风险增大时，省级以上人民政府有关部门应当按照规定的权限和程序，并根据网络安全风险的特点和可能造成的危害，采取下列什么措施？</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21176_a" name="multi_21176" value="a">
                            <label for="multi_21176_a">A. 要求有关部门、机构和人员及时收集、报告有关信息，</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21176_b" name="multi_21176" value="b">
                            <label for="multi_21176_b">B. 向社会发布网络安全风险预警，发布避免、减轻危害的措施。</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21176_c" name="multi_21176" value="c">
                            <label for="multi_21176_c">C. 网络安全危害性不大，不用采取任何措施</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21176_d" name="multi_21176" value="d">
                            <label for="multi_21176_d">D. 组织有关部门、机构和专业人员，对网络安全风险信息进行分析评估，预测事件发生的可能性、影响范围和危害程度；</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">21. 建设关键信息基础设施应当确保其具有支持业务稳定、持续运行的性能，并保证安全技术措施</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21403_a" name="multi_21403" value="a">
                            <label for="multi_21403_a">A. 同步规划</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21403_b" name="multi_21403" value="b">
                            <label for="multi_21403_b">B. 同步建设</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21403_c" name="multi_21403" value="c">
                            <label for="multi_21403_c">C. 同步投运</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21403_d" name="multi_21403" value="d">
                            <label for="multi_21403_d">D. 同步使用</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">22. 网络安全事件发生的风险增大时，省级以上人民政府有关部门应当按照规定的权限和程序，并根据网络安全风险的特点和可能造成的危害，采取下列（）措施。</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21194_a" name="multi_21194" value="a">
                            <label for="multi_21194_a">A. 要求有关部门、机构和人员及时收集、报告有关信息，加强对网络安全风险的监测</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21194_b" name="multi_21194" value="b">
                            <label for="multi_21194_b">B. 向社会发布网络安全风险预警，发布避免、减轻危害的措施</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21194_c" name="multi_21194" value="c">
                            <label for="multi_21194_c">C. 组织有关部门、机构和专业人员，对网络安全风险信息进行分析评估，预测事件发生的可能性、影响范围和危害程度</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21194_d" name="multi_21194" value="d">
                            <label for="multi_21194_d">D. 要求单位和个人协助抓跑嫌犯</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">23. 下列哪些是关键信息设施的运营者应当履行的安全保护义务？</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21191_a" name="multi_21191" value="a">
                            <label for="multi_21191_a">A. 定期对从业人员进行网络安全教育、技术培训和技能考核</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21191_b" name="multi_21191" value="b">
                            <label for="multi_21191_b">B. 对重要系统和数据库进行容灾备份</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21191_c" name="multi_21191" value="c">
                            <label for="multi_21191_c">C. 制定应对网络安全应急预案</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21191_d" name="multi_21191" value="d">
                            <label for="multi_21191_d">D. 设置专门安全管理机构和安全管理负责人</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">24. 国家支持网络运营者之间在网络安全信息（）等方面进行合作，提高网络运营者的安全保障能力。</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21077_a" name="multi_21077" value="a">
                            <label for="multi_21077_a">A. 收集</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21077_b" name="multi_21077" value="b">
                            <label for="multi_21077_b">B. 分析</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21077_c" name="multi_21077" value="c">
                            <label for="multi_21077_c">C. 公开</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21077_d" name="multi_21077" value="d">
                            <label for="multi_21077_d">D. 应急处置</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">25. 在中华人民共和国境内（）网络，以及网络安全的监督管理，适用网络安全法。</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21084_a" name="multi_21084" value="a">
                            <label for="multi_21084_a">A. 建设</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21084_b" name="multi_21084" value="b">
                            <label for="multi_21084_b">B. 运营</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21084_c" name="multi_21084" value="c">
                            <label for="multi_21084_c">C. 维护</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21084_d" name="multi_21084" value="d">
                            <label for="multi_21084_d">D. 使用</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">26. 按照《网络安全法》的有关规定，国家安全审查包含以下哪些事项或活动：</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21224_a" name="multi_21224" value="a">
                            <label for="multi_21224_a">A. 影响国家安全的外商投资项目</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21224_b" name="multi_21224" value="b">
                            <label for="multi_21224_b">B. 影响或者可能影响国家安全的关键技术项目</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21224_c" name="multi_21224" value="c">
                            <label for="multi_21224_c">C. 影响或者可能影响国家安全的网络信息技术产品和服务</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21224_d" name="multi_21224" value="d">
                            <label for="multi_21224_d">D. 涉及国家安全事项的建设项目</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">27. 下列哪些操作可以看到自启动项目（）</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21583_a" name="multi_21583" value="a">
                            <label for="multi_21583_a">A. 注册表的相关键值</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21583_b" name="multi_21583" value="b">
                            <label for="multi_21583_b">B. 在“开始”--“程序”--“启动”项中查看</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21583_c" name="multi_21583" value="c">
                            <label for="multi_21583_c">C. 任务管理器</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21583_d" name="multi_21583" value="d">
                            <label for="multi_21583_d">D. 在“开始”-“运行”中，执行msconfig命令</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">28. 网络钓鱼可以通过以下哪种途径传播（）</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21525_a" name="multi_21525" value="a">
                            <label for="multi_21525_a">A. 通过聊天软件</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21525_b" name="multi_21525" value="b">
                            <label for="multi_21525_b">B. 通过论坛发帖</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21525_c" name="multi_21525" value="c">
                            <label for="multi_21525_c">C. 通过网站浏览</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21525_d" name="multi_21525" value="d">
                            <label for="multi_21525_d">D. 通过查看邮件</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">29. 根据《治安处罚法》规定，以营利为目的，为赌博提供条件的，或者参与赌博赌资较大的，可能被处以以下哪些处罚？</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21212_a" name="multi_21212" value="a">
                            <label for="multi_21212_a">A. 处五日以下拘留</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21212_b" name="multi_21212" value="b">
                            <label for="multi_21212_b">B. 五百元以下罚款</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21212_c" name="multi_21212" value="c">
                            <label for="multi_21212_c">C. 处十日以上十五日以下拘留</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21212_d" name="multi_21212" value="d">
                            <label for="multi_21212_d">D. 五百元罚款</label>
                        </div>

                    </div>
                </div>

                <div class="question">
                    <div class="question-title">30. 计算机病毒能够_____</div>
                    <div class="options">

                        <div class="option">
                            <input type="checkbox" id="multi_21739_a" name="multi_21739" value="a">
                            <label for="multi_21739_a">A. 破坏计算机功能或者毁坏数据</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21739_b" name="multi_21739" value="b">
                            <label for="multi_21739_b">B. 影响计算机使用</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21739_c" name="multi_21739" value="c">
                            <label for="multi_21739_c">C. 能够自我复制</label>
                        </div>

                        <div class="option">
                            <input type="checkbox" id="multi_21739_d" name="multi_21739" value="d">
                            <label for="multi_21739_d">D. 保护版权</label>
                        </div>

                    </div>
                </div>
            </div>

            <button type="submit" class="submit-btn">提交答案</button>
        </form>
    </div>

    <script>
        // 进度条更新
        function updateProgress() {
            const form = document.getElementById('quizForm');
            const inputs = form.querySelectorAll('input[type="radio"], input[type="checkbox"]');
            const questions = new Set();
            
            inputs.forEach(input => {
                const name = input.name;
                questions.add(name);
            });
            
            let answered = 0;
            questions.forEach(questionName => {
                const questionInputs = form.querySelectorAll(`input[name="${questionName}"]`);
                const isAnswered = Array.from(questionInputs).some(input => input.checked);
                if (isAnswered) answered++;
            });
            
            const progress = (answered / questions.size) * 100;
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = progress + '%';
            progressBar.textContent = Math.round(progress) + '%';
        }
        
        // 监听所有输入变化
        document.addEventListener('change', updateProgress);
        
        // 表单提交处理
        document.getElementById('quizForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const answers = {};
            
            for (let [key, value] of formData.entries()) {
                if (answers[key]) {
                    if (Array.isArray(answers[key])) {
                        answers[key].push(value);
                    } else {
                        answers[key] = [answers[key], value];
                    }
                } else {
                    answers[key] = value;
                }
            }
            
            alert('答案已提交！\n\n答题结果：\n' + JSON.stringify(answers, null, 2));
        });
        
        // 初始化进度条
        updateProgress();
    </script>
</body>
</html>
